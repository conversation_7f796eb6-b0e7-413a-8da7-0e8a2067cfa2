import 'strings.dart';

class StringsEn implements Strings {
  @override
  String get nameLabel => 'Name';

  @override
  String get priceLabel => 'Price';

  @override
  String get quantityLabel => 'Quantity';

  @override
  String get totalLabel => 'Total';

  @override
  String get tipLabel => 'Tip';

  @override
  String get percentageLabel => 'Percentage';

  @override
  String get amountLabel => 'Amount';

  @override
  String get nonPositiveNumberError => 'Invalid';

  @override
  String get addItemLabel => '+ Item';

  @override
  String get addTipLabel => '+ Tip';

  @override
  String get continueLabel => 'Continue';

  @override
  String get itemDeleteForbiddenError => 'Cannot delete the only item in the list';

  @override
  String itemIdNotFoundError(String itemId) => 'Item with id "$itemId" not found';

  @override
  String get addPersonLabel => '+ Person';

  @override
  String get closeListLabel => 'Close';

  @override
  String get returnLabel => 'Return';
}