import 'package:flutter/material.dart';

import '../../items/models/item.dart';
import '../models/person.dart';
import 'item_assign.dart';
import 'people_list.dart';

class PeopleAssignmentPage extends StatelessWidget {
  const PeopleAssignmentPage({
    super.key,
    required this.itemList,
    required this.nameLabel,
    required this.onNameChanged,
    required this.isItemExpanded,
    required this.onPeopleListIconPressed,
    required this.getAssignmentSummaryForItem,
    required this.peopleList,
    required this.onPersonNameChanged,
    required this.removePerson,
    required this.decreaseQuantity,
    required this.increaseQuantity,
    required this.isPersonAssignedToItem,
    required this.onAssignedChanged,
    required this.addPersonLabel,
    required this.addPerson,
    required this.closeListLabel,
    required this.onCloseListPressed,
    required this.returnButton,
    required this.total,
    required this.continueButton,
  });

  final List<Item> itemList;
  final String nameLabel;
  final void Function(String itemId, String name) onNameChanged;
  final bool Function(String itemId) isItemExpanded;
  final void Function(String itemId) onPeopleListIconPressed;
  final String Function(String itemId) getAssignmentSummaryForItem;
  final List<Person> peopleList;
  final void Function(String personId, String name) onPersonNameChanged;
  final void Function(String personId) removePerson;
  final void Function(String itemId, String personId) decreaseQuantity;
  final void Function(String itemId, String personId) increaseQuantity;
  final bool Function(String itemId, String personId) isPersonAssignedToItem;
  final void Function(String itemId, String personId, bool isAssigned)
  onAssignedChanged;
  final String addPersonLabel;
  final void Function() addPerson;
  final String closeListLabel;
  final void Function(String itemId) onCloseListPressed;
  final Widget returnButton;
  final Widget total;
  final Widget continueButton;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [returnButton, total, continueButton],
        ),
      ),
      body: SafeArea(
        child: ListView.separated(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          separatorBuilder: (context, index) => const SizedBox(height: 12),
          itemCount: itemList.length,
          itemBuilder: (context, index) {
            final item = itemList[index];
            return ItemAssign(
              orderNumber: index + 1,
              nameLabel: nameLabel,
              name: item.name,
              onNameChanged: (value) => onNameChanged(item.id, value),
              isExpanded: isItemExpanded(item.id),
              onPeopleListIconPressed: () => onPeopleListIconPressed(item.id),
              itemSummary: item.summary,
              assignmentSummary: getAssignmentSummaryForItem(item.id),
              peopleList: PeopleList(
                peopleList: peopleList,
                nameLabel: nameLabel,
                onNameChanged: onPersonNameChanged,
                onDeletePressed: removePerson,
                onSubtractPressed: (personId) =>
                    decreaseQuantity(item.id, personId),
                onAddPressed: (personId) => increaseQuantity(item.id, personId),
                isPersonAssigned: (personId) =>
                    isPersonAssignedToItem(item.id, personId),
                onAssignedChanged: (personId, isAssigned) =>
                    onAssignedChanged(item.id, personId, isAssigned),
              ),
              addPersonLabel: addPersonLabel,
              onPersonAddPressed: addPerson,
              closeListLabel: closeListLabel,
              onCloseListPressed: () => onCloseListPressed(item.id),
            );
          },
        ),
      ),
    );
  }
}
