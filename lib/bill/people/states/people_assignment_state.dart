import 'package:flutter/material.dart';

import '../../../localization/repositories/strings.dart';
import '../../items/models/item.dart';
import '../models/assignment.dart';
import '../models/person.dart';

abstract class PeopleAssignmentState extends ChangeNotifier {
  Strings get strings;

  List<Item> get itemList;
  void onItemNameChanged(String itemId, String name);
  void toggleItemExpansion(String itemId);
  bool isItemExpanded(String itemId);

  List<Person> get peopleList;
  void addPerson();
  void removePerson(String personId);
  void onPersonNameChanged(String personId, String name);

  List<Assignment> get assignmentList;
  String getAssignmentSummaryForItem(String itemId);
  bool isPersonAssignedToItem(String itemId, String personId);
  void onAssignedChanged(String itemId, String personId, bool isAssigned);
  void increaseQuantity(String itemId, String personId);
  void decreaseQuantity(String itemId, String personId);

  String get assignedTotalAndTip;
  String get billTotal;

  void returnToItemDefinition(BuildContext context);
  void continueToPayment(BuildContext context);
}
