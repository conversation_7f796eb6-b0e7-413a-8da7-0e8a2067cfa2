import 'package:flutter/material.dart';

import '../../../components/button/button.dart';
import '../../../locator/repositories/locator.dart';
import '../states/people_assignment_state.dart';
import '../views/assignment_total.dart';
import '../views/people_assignment_page.dart';

class PeopleAssignmentBuilder extends StatelessWidget {
  const PeopleAssignmentBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    final state = Locator.instance.get<PeopleAssignmentState>();
    return ListenableBuilder(
      listenable: state as ChangeNotifier,
      builder: (context, child) {
        return PeopleAssignmentPage(
          itemList: state.itemList,
          nameLabel: state.strings.nameLabel,
          onNameChanged: state.onItemNameChanged,
          isItemExpanded: state.isItemExpanded,
          onPeopleListIconPressed: state.toggleItemExpansion,
          getAssignmentSummaryForItem: state.getAssignmentSummaryForItem,
          peopleList: state.peopleList,
          onPersonNameChanged: state.onPersonNameChanged,
          removePerson: state.removePerson,
          decreaseQuantity: state.decreaseQuantity,
          increaseQuantity: state.increaseQuantity,
          isPersonAssignedToItem: state.isPersonAssignedToItem,
          onAssignedChanged: state.onAssignedChanged,
          addPersonLabel: state.strings.addPersonLabel,
          addPerson: state.addPerson,
          closeListLabel: state.strings.closeListLabel,
          onCloseListPressed: (itemId) => state.toggleItemExpansion(itemId),
          returnButton: Button(
            label: state.strings.returnLabel,
            onPressed: () => state.returnToItemDefinition(context),
          ),
          total: AssignmentTotal(
            firstLine: state.assignedTotalAndTip,
            secondLine: state.billTotal,
          ),
          continueButton: Button(
            label: state.strings.continueLabel,
            onPressed: () => state.continueToPayment(context),
          ),
        );
      },
    );
  }
}
